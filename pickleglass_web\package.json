{"name": "pickleglass-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.17", "autoprefixer": "^10.4.16", "axios": "^1.6.0", "firebase": "^11.10.0", "lucide-react": "^0.294.0", "next": "^14.2.30", "postcss": "^8.4.32", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.4", "typescript": "^5"}}