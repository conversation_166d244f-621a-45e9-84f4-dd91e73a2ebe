var createAecModule = (() => {
  var _scriptName = typeof document != 'undefined' ? document.currentScript?.src : undefined;
  return (
async function(moduleArg = {}) {
  var moduleRtn;

var Module=moduleArg;var ENVIRONMENT_IS_WEB=typeof window=="object";var ENVIRONMENT_IS_WORKER=typeof WorkerGlobalScope!="undefined";var ENVIRONMENT_IS_NODE=typeof process=="object"&&process.versions?.node&&process.type!="renderer";var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};if(ENVIRONMENT_IS_WORKER){_scriptName=self.location.href}var scriptDirectory="";var readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){try{scriptDirectory=new URL(".",_scriptName).href}catch{}{if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=async url=>{var response=await fetch(url,{credentials:"same-origin"});if(response.ok){return response.arrayBuffer()}throw new Error(response.status+" : "+response.url)}}}else{}var out=console.log.bind(console);var err=console.error.bind(console);var wasmBinary;var ABORT=false;var EXITSTATUS;var readyPromiseResolve,readyPromiseReject;var wasmMemory;var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;var HEAP64,HEAPU64;var runtimeInitialized=false;function updateMemoryViews(){var b=wasmMemory.buffer;HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);HEAPU16=new Uint16Array(b);HEAP32=new Int32Array(b);HEAPU32=new Uint32Array(b);HEAPF32=new Float32Array(b);HEAPF64=new Float64Array(b);HEAP64=new BigInt64Array(b);HEAPU64=new BigUint64Array(b)}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(onPreRuns)}function initRuntime(){runtimeInitialized=true;if(!Module["noFSInit"]&&!FS.initialized)FS.init();TTY.init();wasmExports["v"]();FS.ignorePermissions=false}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(onPostRuns)}var runDependencies=0;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;Module["monitorRunDependencies"]?.(runDependencies)}function removeRunDependency(id){runDependencies--;Module["monitorRunDependencies"]?.(runDependencies);if(runDependencies==0){if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){Module["onAbort"]?.(what);what="Aborted("+what+")";err(what);ABORT=true;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject?.(e);throw e}var wasmBinaryFile;function findWasmBinary(){return base64Decode("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")}function getBinarySync(file){if(ArrayBuffer.isView(file)){return file}if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}async function getWasmBinary(binaryFile){return getBinarySync(binaryFile)}async function instantiateArrayBuffer(binaryFile,imports){try{var binary=await getWasmBinary(binaryFile);var instance=await WebAssembly.instantiate(binary,imports);return instance}catch(reason){err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)}}async function instantiateAsync(binary,binaryFile,imports){return instantiateArrayBuffer(binaryFile,imports)}function getWasmImports(){return{a:wasmImports}}async function createWasm(){function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports["u"];updateMemoryViews();wasmTable=wasmExports["H"];assignWasmExports(wasmExports);removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){return receiveInstance(result["instance"])}var info=getWasmImports();if(Module["instantiateWasm"]){return new Promise((resolve,reject)=>{Module["instantiateWasm"](info,(mod,inst)=>{resolve(receiveInstance(mod,inst))})})}wasmBinaryFile??=findWasmBinary();var result=await instantiateAsync(wasmBinary,wasmBinaryFile,info);var exports=receiveInstantiationResult(result);return exports}class ExitStatus{name="ExitStatus";constructor(status){this.message=`Program terminated with exit(${status})`;this.status=status}}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var onPostRuns=[];var addOnPostRun=cb=>onPostRuns.push(cb);var onPreRuns=[];var addOnPreRun=cb=>onPreRuns.push(cb);var base64Decode=b64=>{var b1,b2,i=0,j=0,bLength=b64.length;var output=new Uint8Array((bLength*3>>2)-(b64[bLength-2]=="=")-(b64[bLength-1]=="="));for(;i<bLength;i+=4,j+=3){b1=base64ReverseLookup[b64.charCodeAt(i+1)];b2=base64ReverseLookup[b64.charCodeAt(i+2)];output[j]=base64ReverseLookup[b64.charCodeAt(i)]<<2|b1>>4;output[j+1]=b1<<4|b2>>2;output[j+2]=b2<<6|base64ReverseLookup[b64.charCodeAt(i+3)]}return output};var noExitRuntime=true;var stackRestore=val=>__emscripten_stack_restore(val);var stackSave=()=>_emscripten_stack_get_current();var exceptionLast=0;class ExceptionInfo{constructor(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24}set_type(type){HEAPU32[this.ptr+4>>2]=type}get_type(){return HEAPU32[this.ptr+4>>2]}set_destructor(destructor){HEAPU32[this.ptr+8>>2]=destructor}get_destructor(){return HEAPU32[this.ptr+8>>2]}set_caught(caught){caught=caught?1:0;HEAP8[this.ptr+12]=caught}get_caught(){return HEAP8[this.ptr+12]!=0}set_rethrown(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13]=rethrown}get_rethrown(){return HEAP8[this.ptr+13]!=0}init(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)}set_adjusted_ptr(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr}get_adjusted_ptr(){return HEAPU32[this.ptr+16>>2]}}var setTempRet0=val=>__emscripten_tempret_set(val);var findMatchingCatch=args=>{var thrown=exceptionLast;if(!thrown){setTempRet0(0);return 0}var info=new ExceptionInfo(thrown);info.set_adjusted_ptr(thrown);var thrownType=info.get_type();if(!thrownType){setTempRet0(0);return thrown}for(var caughtType of args){if(caughtType===0||caughtType===thrownType){break}var adjusted_ptr_addr=info.ptr+16;if(___cxa_can_catch(caughtType,thrownType,adjusted_ptr_addr)){setTempRet0(caughtType);return thrown}}setTempRet0(thrownType);return thrown};var ___cxa_find_matching_catch_2=()=>findMatchingCatch([]);var uncaughtExceptionCount=0;var ___cxa_throw=(ptr,type,destructor)=>{var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast};var ___resumeException=ptr=>{if(!exceptionLast){exceptionLast=ptr}throw exceptionLast};var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.codePointAt(i);if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63;i++}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);function ___syscall_getcwd(buf,size){try{if(size===0)return-28;var cwd=FS.cwd();var cwdLengthInBytes=lengthBytesUTF8(cwd)+1;if(size<cwdLengthInBytes)return-68;stringToUTF8(cwd,buf,size);return cwdLengthInBytes}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}var __abort_js=()=>abort("");var getHeapMax=()=>2147483648;var alignMemory=(size,alignment)=>Math.ceil(size/alignment)*alignment;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536|0;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignMemory(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false};var ENV={};var getExecutableName=()=>thisProgram||"./this.program";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.language||"C").replace("-","_")+".UTF-8";var env={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:lang,_:getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;var envp=0;for(var string of getEnvStrings()){var ptr=environ_buf+bufSize;HEAPU32[__environ+envp>>2]=ptr;bufSize+=stringToUTF8(string,ptr,Infinity)+1;envp+=4}return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;for(var string of strings){bufSize+=lengthBytesUTF8(string)+1}HEAPU32[penviron_buf_size>>2]=bufSize;return 0};var runtimeKeepaliveCounter=0;var keepRuntimeAlive=()=>noExitRuntime||runtimeKeepaliveCounter>0;var _proc_exit=code=>{EXITSTATUS=code;if(!keepRuntimeAlive()){Module["onExit"]?.(code);ABORT=true}quit_(code,new ExitStatus(code))};var exitJS=(status,implicit)=>{EXITSTATUS=status;_proc_exit(status)};var _exit=exitJS;var PATH={isAbs:path=>path.charAt(0)==="/",splitPath:filename=>{var splitPathRe=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last==="."){parts.splice(i,1)}else if(last===".."){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift("..")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.slice(-1)==="/";path=PATH.normalizeArray(path.split("/").filter(p=>!!p),!isAbsolute).join("/");if(!path&&!isAbsolute){path="."}if(path&&trailingSlash){path+="/"}return(isAbsolute?"/":"")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return"."}if(dir){dir=dir.slice(0,-1)}return root+dir},basename:path=>path&&path.match(/([^\/]+|\/)\/*$/)[1],join:(...paths)=>PATH.normalize(paths.join("/")),join2:(l,r)=>PATH.normalize(l+"/"+r)};var initRandomFill=()=>view=>crypto.getRandomValues(view);var randomFill=view=>{(randomFill=initRandomFill())(view)};var PATH_FS={resolve:(...args)=>{var resolvedPath="",resolvedAbsolute=false;for(var i=args.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?args[i]:FS.cwd();if(typeof path!="string"){throw new TypeError("Arguments to path.resolve must be strings")}else if(!path){return""}resolvedPath=path+"/"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split("/").filter(p=>!!p),!resolvedAbsolute).join("/");return(resolvedAbsolute?"/":"")+resolvedPath||"."},relative:(from,to)=>{from=PATH_FS.resolve(from).slice(1);to=PATH_FS.resolve(to).slice(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!=="")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!=="")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split("/"));var toParts=trim(to.split("/"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push("..")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join("/")}};var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder:undefined;var UTF8ArrayToString=(heapOrArray,idx=0,maxBytesToRead=NaN)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var FS_stdin_getChar_buffer=[];var intArrayFromString=(stringy,dontAddNull,length)=>{var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array};var FS_stdin_getChar=()=>{if(!FS_stdin_getChar_buffer.length){var result=null;if(typeof window!="undefined"&&typeof window.prompt=="function"){result=window.prompt("Input: ");if(result!==null){result+="\n"}}else{}if(!result){return null}FS_stdin_getChar_buffer=intArrayFromString(result,true)}return FS_stdin_getChar_buffer.shift()};var TTY={ttys:[],init(){},shutdown(){},register(dev,ops){TTY.ttys[dev]={input:[],output:[],ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close(stream){stream.tty.ops.fsync(stream.tty)},fsync(stream){stream.tty.ops.fsync(stream.tty)},read(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.atime=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.mtime=stream.node.ctime=Date.now()}return i}},default_tty_ops:{get_char(tty){return FS_stdin_getChar()},put_char(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output?.length>0){out(UTF8ArrayToString(tty.output));tty.output=[]}},ioctl_tcgets(tty){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets(tty,optional_actions,data){return 0},ioctl_tiocgwinsz(tty){return[24,80]}},default_tty1_ops:{put_char(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output?.length>0){err(UTF8ArrayToString(tty.output));tty.output=[]}}}};var mmapAlloc=size=>{abort()};var MEMFS={ops_table:null,mount(mount){return MEMFS.createNode(null,"/",16895,0)},createNode(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}MEMFS.ops_table||={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}};var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.atime=node.mtime=node.ctime=Date.now();if(parent){parent.contents[name]=node;parent.atime=parent.mtime=parent.ctime=node.atime}return node},getFileDataAsTypedArray(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.atime);attr.mtime=new Date(node.mtime);attr.ctime=new Date(node.ctime);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr(node,attr){for(const key of["mode","atime","mtime","ctime"]){if(attr[key]!=null){node[key]=attr[key]}}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup(parent,name){throw MEMFS.doesNotExistError},mknod(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename(old_node,new_dir,new_name){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){if(FS.isDir(old_node.mode)){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}FS.hashRemoveNode(new_node)}delete old_node.parent.contents[old_node.name];new_dir.contents[new_name]=old_node;old_node.name=new_name;new_dir.ctime=new_dir.mtime=old_node.parent.ctime=old_node.parent.mtime=Date.now()},unlink(parent,name){delete parent.contents[name];parent.ctime=parent.mtime=Date.now()},rmdir(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.ctime=parent.mtime=Date.now()},readdir(node){return[".","..",...Object.keys(node.contents)]},symlink(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write(stream,buffer,offset,length,position,canOwn){if(buffer.buffer===HEAP8.buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.mtime=node.ctime=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},mmap(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents&&contents.buffer===HEAP8.buffer){allocated=false;ptr=contents.byteOffset}else{allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}if(contents){if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}HEAP8.set(contents,ptr)}}return{ptr,allocated}},msync(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};var asyncLoad=async url=>{var arrayBuffer=await readAsync(url);return new Uint8Array(arrayBuffer)};var FS_createDataFile=(...args)=>FS.createDataFile(...args);var getUniqueRunDependency=id=>id;var preloadPlugins=[];var FS_handledByPreloadPlugin=(byteArray,fullname,finish,onerror)=>{if(typeof Browser!="undefined")Browser.init();var handled=false;preloadPlugins.forEach(plugin=>{if(handled)return;if(plugin["canHandle"](fullname)){plugin["handle"](byteArray,fullname,finish,onerror);handled=true}});return handled};var FS_createPreloadedFile=(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish)=>{var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){preFinish?.();if(!dontCreateFile){FS_createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}onload?.();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{onerror?.();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url=="string"){asyncLoad(url).then(processData,onerror)}else{processData(url)}};var FS_modeStringToFlags=str=>{var flagModes={r:0,"r+":2,w:512|64|1,"w+":512|64|2,a:1024|64|1,"a+":1024|64|2};var flags=flagModes[str];if(typeof flags=="undefined"){throw new Error(`Unknown file open mode: ${str}`)}return flags};var FS_getMode=(canRead,canWrite)=>{var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,filesystems:null,syncFSRequests:0,readFiles:{},ErrnoError:class{name="ErrnoError";constructor(errno){this.errno=errno}},FSStream:class{shared={};get object(){return this.node}set object(val){this.node=val}get isRead(){return(this.flags&2097155)!==1}get isWrite(){return(this.flags&2097155)!==0}get isAppend(){return this.flags&1024}get flags(){return this.shared.flags}set flags(val){this.shared.flags=val}get position(){return this.shared.position}set position(val){this.shared.position=val}},FSNode:class{node_ops={};stream_ops={};readMode=292|73;writeMode=146;mounted=null;constructor(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.rdev=rdev;this.atime=this.mtime=this.ctime=Date.now()}get read(){return(this.mode&this.readMode)===this.readMode}set read(val){val?this.mode|=this.readMode:this.mode&=~this.readMode}get write(){return(this.mode&this.writeMode)===this.writeMode}set write(val){val?this.mode|=this.writeMode:this.mode&=~this.writeMode}get isFolder(){return FS.isDir(this.mode)}get isDevice(){return FS.isChrdev(this.mode)}},lookupPath(path,opts={}){if(!path){throw new FS.ErrnoError(44)}opts.follow_mount??=true;if(!PATH.isAbs(path)){path=FS.cwd()+"/"+path}linkloop:for(var nlinks=0;nlinks<40;nlinks++){var parts=path.split("/").filter(p=>!!p);var current=FS.root;var current_path="/";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}if(parts[i]==="."){continue}if(parts[i]===".."){current_path=PATH.dirname(current_path);if(FS.isRoot(current)){path=current_path+"/"+parts.slice(i+1).join("/");continue linkloop}else{current=current.parent}continue}current_path=PATH.join2(current_path,parts[i]);try{current=FS.lookupNode(current,parts[i])}catch(e){if(e?.errno===44&&islast&&opts.noent_okay){return{path:current_path}}throw e}if(FS.isMountpoint(current)&&(!islast||opts.follow_mount)){current=current.mounted.root}if(FS.isLink(current.mode)&&(!islast||opts.follow)){if(!current.node_ops.readlink){throw new FS.ErrnoError(52)}var link=current.node_ops.readlink(current);if(!PATH.isAbs(link)){link=PATH.dirname(current_path)+"/"+link}path=link+"/"+parts.slice(i+1).join("/");continue linkloop}}return{path:current_path,node:current}}throw new FS.ErrnoError(32)},getPath(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!=="/"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode(parent,name){var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode(parent,name,mode,rdev){var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode(node){FS.hashRemoveNode(node)},isRoot(node){return node===node.parent},isMountpoint(node){return!!node.mounted},isFile(mode){return(mode&61440)===32768},isDir(mode){return(mode&61440)===16384},isLink(mode){return(mode&61440)===40960},isChrdev(mode){return(mode&61440)===8192},isBlkdev(mode){return(mode&61440)===24576},isFIFO(mode){return(mode&61440)===4096},isSocket(mode){return(mode&49152)===49152},flagsToPermissionString(flag){var perms=["r","w","rw"][flag&3];if(flag&512){perms+="w"}return perms},nodePermissions(node,perms){if(FS.ignorePermissions){return 0}if(perms.includes("r")&&!(node.mode&292)){return 2}else if(perms.includes("w")&&!(node.mode&146)){return 2}else if(perms.includes("x")&&!(node.mode&73)){return 2}return 0},mayLookup(dir){if(!FS.isDir(dir.mode))return 54;var errCode=FS.nodePermissions(dir,"x");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate(dir,name){if(!FS.isDir(dir.mode)){return 54}try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,"wx")},mayDelete(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,"wx");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen(node,flags){if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!=="r"||flags&(512|64)){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},checkOpExists(op,err){if(!op){throw new FS.ErrnoError(err)}return op},MAX_OPEN_FDS:4096,nextfd(){for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStreamChecked(fd){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}return stream},getStream:fd=>FS.streams[fd],createStream(stream,fd=-1){stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream(fd){FS.streams[fd]=null},dupStream(origStream,fd=-1){var stream=FS.createStream(origStream,fd);stream.stream_ops?.dup?.(stream);return stream},doSetAttr(stream,node,attr){var setattr=stream?.stream_ops.setattr;var arg=setattr?stream:node;setattr??=node.node_ops.setattr;FS.checkOpExists(setattr,63);setattr(arg,attr)},chrdev_stream_ops:{open(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;stream.stream_ops.open?.(stream)},llseek(){throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice(dev,ops){FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push(...m.mounts)}return mounts},syncfs(populate,callback){if(typeof populate=="function"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount(type,opts,mountpoint){var root=mountpoint==="/";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type,opts,mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);node.mount.mounts.splice(idx,1)},lookup(parent,name){return parent.node_ops.lookup(parent,name)},mknod(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name){throw new FS.ErrnoError(28)}if(name==="."||name===".."){throw new FS.ErrnoError(20)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},statfs(path){return FS.statfsNode(FS.lookupPath(path,{follow:true}).node)},statfsStream(stream){return FS.statfsNode(stream.node)},statfsNode(node){var rtn={bsize:4096,frsize:4096,blocks:1e6,bfree:5e5,bavail:5e5,files:FS.nextInode,ffree:FS.nextInode-1,fsid:42,flags:2,namelen:255};if(node.node_ops.statfs){Object.assign(rtn,node.node_ops.statfs(node.mount.opts.root))}return rtn},create(path,mode=438){mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir(path,mode=511){mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree(path,mode){var dirs=path.split("/");var d="";for(var dir of dirs){if(!dir)continue;if(d||PATH.isAbs(path))d+="/";d+=dir;try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev(path,mode,dev){if(typeof dev=="undefined"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink(oldpath,newpath){if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,"w");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name);old_node.parent=new_dir}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;var readdir=FS.checkOpExists(node.node_ops.readdir,54);return readdir(node)},unlink(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return link.node_ops.readlink(link)},stat(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;var getattr=FS.checkOpExists(node.node_ops.getattr,63);return getattr(node)},fstat(fd){var stream=FS.getStreamChecked(fd);var node=stream.node;var getattr=stream.stream_ops.getattr;var arg=getattr?stream:node;getattr??=node.node_ops.getattr;FS.checkOpExists(getattr,63);return getattr(arg)},lstat(path){return FS.stat(path,true)},doChmod(stream,node,mode,dontFollow){FS.doSetAttr(stream,node,{mode:mode&4095|node.mode&~4095,ctime:Date.now(),dontFollow})},chmod(path,mode,dontFollow){var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}FS.doChmod(null,node,mode,dontFollow)},lchmod(path,mode){FS.chmod(path,mode,true)},fchmod(fd,mode){var stream=FS.getStreamChecked(fd);FS.doChmod(stream,stream.node,mode,false)},doChown(stream,node,dontFollow){FS.doSetAttr(stream,node,{timestamp:Date.now(),dontFollow})},chown(path,uid,gid,dontFollow){var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}FS.doChown(null,node,dontFollow)},lchown(path,uid,gid){FS.chown(path,uid,gid,true)},fchown(fd,uid,gid){var stream=FS.getStreamChecked(fd);FS.doChown(stream,stream.node,false)},doTruncate(stream,node,len){if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,"w");if(errCode){throw new FS.ErrnoError(errCode)}FS.doSetAttr(stream,node,{size:len,timestamp:Date.now()})},truncate(path,len){if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}FS.doTruncate(null,node,len)},ftruncate(fd,len){var stream=FS.getStreamChecked(fd);if(len<0||(stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.doTruncate(stream,stream.node,len)},utime(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;var setattr=FS.checkOpExists(node.node_ops.setattr,63);setattr(node,{atime,mtime})},open(path,flags,mode=438){if(path===""){throw new FS.ErrnoError(44)}flags=typeof flags=="string"?FS_modeStringToFlags(flags):flags;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;var isDirPath;if(typeof path=="object"){node=path}else{isDirPath=path.endsWith("/");var lookup=FS.lookupPath(path,{follow:!(flags&131072),noent_okay:true});node=lookup.node;path=lookup.path}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else if(isDirPath){throw new FS.ErrnoError(31)}else{node=FS.mknod(path,mode|511,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node,path:FS.getPath(node),flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(created){FS.chmod(node,mode&511)}if(Module["logReadFiles"]&&!(flags&1)){if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed(stream){return stream.fd===null},llseek(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read(stream,buffer,offset,length,position){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write(stream,buffer,offset,length,position,canOwn){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},mmap(stream,length,position,prot,flags){if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}if(!length){throw new FS.ErrnoError(28)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync(stream,buffer,offset,length,mmapFlags){if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},ioctl(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile(path,opts={}){opts.flags=opts.flags||0;opts.encoding=opts.encoding||"binary";if(opts.encoding!=="utf8"&&opts.encoding!=="binary"){throw new Error(`Invalid encoding type "${opts.encoding}"`)}var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding==="utf8"){buf=UTF8ArrayToString(buf)}FS.close(stream);return buf},writeFile(path,data,opts={}){opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data=="string"){data=new Uint8Array(intArrayFromString(data,true))}if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error("Unsupported data type")}FS.close(stream)},cwd:()=>FS.currentPath,chdir(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,"x");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories(){FS.mkdir("/tmp");FS.mkdir("/home");FS.mkdir("/home/<USER>")},createDefaultDevices(){FS.mkdir("/dev");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length,llseek:()=>0});FS.mkdev("/dev/null",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev("/dev/tty",FS.makedev(5,0));FS.mkdev("/dev/tty1",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomFill(randomBuffer);randomLeft=randomBuffer.byteLength}return randomBuffer[--randomLeft]};FS.createDevice("/dev","random",randomByte);FS.createDevice("/dev","urandom",randomByte);FS.mkdir("/dev/shm");FS.mkdir("/dev/shm/tmp")},createSpecialDirectories(){FS.mkdir("/proc");var proc_self=FS.mkdir("/proc/self");FS.mkdir("/proc/self/fd");FS.mount({mount(){var node=FS.createNode(proc_self,"fd",16895,73);node.stream_ops={llseek:MEMFS.stream_ops.llseek};node.node_ops={lookup(parent,name){var fd=+name;var stream=FS.getStreamChecked(fd);var ret={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>stream.path},id:fd+1};ret.parent=ret;return ret},readdir(){return Array.from(FS.streams.entries()).filter(([k,v])=>v).map(([k,v])=>k.toString())}};return node}},{},"/proc/self/fd")},createStandardStreams(input,output,error){if(input){FS.createDevice("/dev","stdin",input)}else{FS.symlink("/dev/tty","/dev/stdin")}if(output){FS.createDevice("/dev","stdout",null,output)}else{FS.symlink("/dev/tty","/dev/stdout")}if(error){FS.createDevice("/dev","stderr",null,error)}else{FS.symlink("/dev/tty1","/dev/stderr")}var stdin=FS.open("/dev/stdin",0);var stdout=FS.open("/dev/stdout",1);var stderr=FS.open("/dev/stderr",1)},staticInit(){FS.nameTable=new Array(4096);FS.mount(MEMFS,{},"/");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={MEMFS}},init(input,output,error){FS.initialized=true;input??=Module["stdin"];output??=Module["stdout"];error??=Module["stderr"];FS.createStandardStreams(input,output,error)},quit(){FS.initialized=false;for(var stream of FS.streams){if(stream){FS.close(stream)}}},findObject(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path==="/"}catch(e){ret.error=e.errno}return ret},createPath(parent,path,canRead,canWrite){parent=typeof parent=="string"?parent:FS.getPath(parent);var parts=path.split("/").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){if(e.errno!=20)throw e}parent=current}return current},createFile(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile(parent,name,data,canRead,canWrite,canOwn){var path=name;if(parent){parent=typeof parent=="string"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data=="string"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}},createDevice(parent,name,input,output){var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);FS.createDevice.major??=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open(stream){stream.seekable=false},close(stream){if(output?.buffer?.length){output(10)}},read(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.atime=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.mtime=stream.node.ctime=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!="undefined"){throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.")}else{try{obj.contents=readBinary(obj.url);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}},createLazyFile(parent,name,url,canRead,canWrite){class LazyUint8Array{lengthKnown=false;chunks=[];get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]}setDataGetter(getter){this.getter=getter}cacheLength(){var xhr=new XMLHttpRequest;xhr.open("HEAD",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);var datalength=Number(xhr.getResponseHeader("Content-length"));var header;var hasByteServing=(header=xhr.getResponseHeader("Accept-Ranges"))&&header==="bytes";var usesGzip=(header=xhr.getResponseHeader("Content-Encoding"))&&header==="gzip";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error("invalid range ("+from+", "+to+") or no bytes requested!");if(to>datalength-1)throw new Error("only "+datalength+" bytes available! programmer error!");var xhr=new XMLHttpRequest;xhr.open("GET",url,false);if(datalength!==chunkSize)xhr.setRequestHeader("Range","bytes="+from+"-"+to);xhr.responseType="arraybuffer";if(xhr.overrideMimeType){xhr.overrideMimeType("text/plain; charset=x-user-defined")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||"",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]=="undefined"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]=="undefined")throw new Error("doXHR failed!");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out("LazyFiles on gzip forces download of the whole file when length is accessed")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true}get length(){if(!this.lengthKnown){this.cacheLength()}return this._length}get chunkSize(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}if(typeof XMLHttpRequest!="undefined"){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var lazyArray=new LazyUint8Array;var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=(...args)=>{FS.forceLoadFile(node);return fn(...args)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,HEAP8,ptr,length,position);return{ptr,allocated:true}};node.stream_ops=stream_ops;return node}};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):"";var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return dir+"/"+path},writeStat(buf,stat){HEAP32[buf>>2]=stat.dev;HEAP32[buf+4>>2]=stat.mode;HEAPU32[buf+8>>2]=stat.nlink;HEAP32[buf+12>>2]=stat.uid;HEAP32[buf+16>>2]=stat.gid;HEAP32[buf+20>>2]=stat.rdev;HEAP64[buf+24>>3]=BigInt(stat.size);HEAP32[buf+32>>2]=4096;HEAP32[buf+36>>2]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();HEAP64[buf+40>>3]=BigInt(Math.floor(atime/1e3));HEAPU32[buf+48>>2]=atime%1e3*1e3*1e3;HEAP64[buf+56>>3]=BigInt(Math.floor(mtime/1e3));HEAPU32[buf+64>>2]=mtime%1e3*1e3*1e3;HEAP64[buf+72>>3]=BigInt(Math.floor(ctime/1e3));HEAPU32[buf+80>>2]=ctime%1e3*1e3*1e3;HEAP64[buf+88>>3]=BigInt(stat.ino);return 0},writeStatFs(buf,stats){HEAP32[buf+4>>2]=stats.bsize;HEAP32[buf+40>>2]=stats.bsize;HEAP32[buf+8>>2]=stats.blocks;HEAP32[buf+12>>2]=stats.bfree;HEAP32[buf+16>>2]=stats.bavail;HEAP32[buf+20>>2]=stats.files;HEAP32[buf+24>>2]=stats.ffree;HEAP32[buf+28>>2]=stats.fsid;HEAP32[buf+44>>2]=stats.flags;HEAP32[buf+36>>2]=stats.namelen},doMsync(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=HEAPU8.slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},getStreamFromFD(fd){var stream=FS.getStreamChecked(fd);return stream},varargs:undefined,getStr(ptr){var ret=UTF8ToString(ptr);return ret}};function _fd_close(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var INT53_MAX=9007199254740992;var INT53_MIN=-9007199254740992;var bigintToI53Checked=num=>num<INT53_MIN||num>INT53_MAX?NaN:Number(num);function _fd_seek(fd,offset,whence,newOffset){offset=bigintToI53Checked(offset);try{if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);HEAP64[newOffset>>3]=BigInt(stream.position);if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var doWritev=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len){break}if(typeof offset!="undefined"){offset+=curr}}return ret};function _fd_write(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};var getCFunc=ident=>{var func=Module["_"+ident];return func};var writeArrayToMemory=(array,buffer)=>{HEAP8.set(array,buffer)};var stackAlloc=sz=>__emscripten_stack_alloc(sz);var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var ccall=(ident,returnType,argTypes,args,opts)=>{var toC={string:str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},array:arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string"){return UTF8ToString(ret)}if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func(...cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret};var cwrap=(ident,returnType,argTypes,opts)=>{var numericArgs=!argTypes||argTypes.every(type=>type==="number"||type==="boolean");var numericRet=returnType!=="string";if(numericRet&&numericArgs&&!opts){return getCFunc(ident)}return(...args)=>ccall(ident,returnType,argTypes,args,opts)};for(var base64ReverseLookup=new Uint8Array(123),i=25;i>=0;--i){base64ReverseLookup[48+i]=52+i;base64ReverseLookup[65+i]=i;base64ReverseLookup[97+i]=26+i}base64ReverseLookup[43]=62;base64ReverseLookup[47]=63;FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();MEMFS.doesNotExistError=new FS.ErrnoError(44);MEMFS.doesNotExistError.stack="<generic error, no stack>";{if(Module["noExitRuntime"])noExitRuntime=Module["noExitRuntime"];if(Module["preloadPlugins"])preloadPlugins=Module["preloadPlugins"];if(Module["print"])out=Module["print"];if(Module["printErr"])err=Module["printErr"];if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"]}Module["ccall"]=ccall;Module["cwrap"]=cwrap;var _AecNew,_AecCancelEcho,_AecDestroy,_malloc,_free,_setThrew,__emscripten_tempret_set,__emscripten_stack_restore,__emscripten_stack_alloc,_emscripten_stack_get_current,___cxa_can_catch;function assignWasmExports(wasmExports){Module["_AecNew"]=_AecNew=wasmExports["w"];Module["_AecCancelEcho"]=_AecCancelEcho=wasmExports["x"];Module["_AecDestroy"]=_AecDestroy=wasmExports["y"];Module["_malloc"]=_malloc=wasmExports["z"];Module["_free"]=_free=wasmExports["A"];_setThrew=wasmExports["B"];__emscripten_tempret_set=wasmExports["C"];__emscripten_stack_restore=wasmExports["D"];__emscripten_stack_alloc=wasmExports["E"];_emscripten_stack_get_current=wasmExports["F"];___cxa_can_catch=wasmExports["G"]}var wasmImports={a:___cxa_find_matching_catch_2,q:___cxa_throw,b:___resumeException,p:___syscall_getcwd,r:__abort_js,t:_emscripten_resize_heap,n:_environ_get,o:_environ_sizes_get,k:_exit,s:_fd_close,l:_fd_seek,g:_fd_write,i:invoke_ii,f:invoke_iiii,m:invoke_iiiiii,c:invoke_vi,d:invoke_vii,e:invoke_viii,j:invoke_viiii,h:invoke_viiiii};var wasmExports=await createWasm();function invoke_vi(index,a1){var sp=stackSave();try{getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viii(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vii(index,a1,a2){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiii(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_ii(index,a1){var sp=stackSave();try{return getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function run(){if(runDependencies>0){dependenciesFulfilled=run;return}preRun();if(runDependencies>0){dependenciesFulfilled=run;return}function doRun(){Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve?.(Module);Module["onRuntimeInitialized"]?.();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(()=>{setTimeout(()=>Module["setStatus"](""),1);doRun()},1)}else{doRun()}}function preInit(){if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].shift()()}}}preInit();run();if(runtimeInitialized){moduleRtn=Module}else{moduleRtn=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject})}


  return moduleRtn;
}
);
})();
if (typeof exports === 'object' && typeof module === 'object') {
  module.exports = createAecModule;
  // This default export looks redundant, but it allows TS to import this
  // commonjs style module.
  module.exports.default = createAecModule;
} else if (typeof define === 'function' && define['amd'])
  define([], () => createAecModule);
