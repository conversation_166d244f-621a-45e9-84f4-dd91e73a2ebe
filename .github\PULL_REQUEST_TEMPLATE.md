---
name: Pull Request
about: Propose a change to the codebase
---

## Summary of Changes

Please provide a brief, high-level summary of the changes in this pull request.

## Related Issue

- Closes #XXX

*Please replace `XXX` with the issue number that this pull request resolves. If it does not resolve a specific issue, please explain why this change is needed.*

## Contributor's Self-Review Checklist

Please check the boxes that apply. This is a reminder of what we look for in a good pull request.

- [ ] I have read the [CONTRIBUTING.md](https://github.com/your-org/your-repo/blob/main/CONTRIBUTING.md) document.
- [ ] My code follows the project's coding style and architectural patterns as described in [DESIGN_PATTERNS.md](https://github.com/your-org/your-repo/blob/main/docs/DESIGN_PATTERNS.md).
- [ ] I have added or updated relevant tests for my changes.
- [ ] I have updated the documentation to reflect my changes (if applicable).
- [ ] My changes have been tested locally and are working as expected.

## Additional Context (Optional)

Add any other context or screenshots about the pull request here. 